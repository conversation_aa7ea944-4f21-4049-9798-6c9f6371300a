import {
  Code,
  ShoppingCart,
  Database,
  Cloud,
  Laptop,
  Server,
  Palette,
  Gauge,
  Globe,
  Smartphone,
  Shield,
  Zap,
  Users,
  BarChart3,
  <PERSON><PERSON>s,
  Layers
} from "lucide-react";
import { Product, ProductCategory } from "@/types/products";

export const productCategories: ProductCategory[] = [
  {
    id: 'web-development',
    name: 'Web Development',
    description: 'Custom websites and web applications',
    icon: Code
  },
  {
    id: 'ecommerce',
    name: 'E-commerce Solutions',
    description: 'Online stores and marketplace platforms',
    icon: ShoppingCart
  },
  {
    id: 'mobile-apps',
    name: 'Mobile Applications',
    description: 'iOS and Android mobile applications',
    icon: Smartphone
  },
  {
    id: 'cloud-solutions',
    name: 'Cloud Solutions',
    description: 'Cloud infrastructure and deployment',
    icon: Cloud
  },
  {
    id: 'consulting',
    name: 'Technical Consulting',
    description: 'Expert guidance and strategy',
    icon: Users
  }
];

export const products: Product[] = [
  {
    id: 'custom-web-app',
    title: 'Custom Web Application',
    shortDescription: 'Tailored web applications built with modern technologies',
    description: 'Full-stack web applications designed specifically for your business needs, built with React, Node.js, and modern cloud technologies.',
    icon: Code,
    category: productCategories[0],
    features: [
      'Modern React/Next.js frontend',
      'Node.js/Python backend',
      'Database design & optimization',
      'API development & integration',
      'Authentication & security',
      'Responsive design',
      'Performance optimization',
      'Deployment & hosting'
    ],
    technologies: ['React', 'Next.js', 'Node.js', 'TypeScript', 'PostgreSQL', 'AWS'],
    pricing: {
      type: 'starting',
      amount: 5000,
      currency: 'USD',
      note: 'Price varies based on complexity and features'
    },
    popular: true,
    featured: true,
    testimonial: {
      client: 'Sarah Johnson',
      company: 'TechStart Inc.',
      content: 'CodeSafir delivered exactly what we needed. The web application is fast, secure, and our users love it.',
      rating: 5
    }
  },
  {
    id: 'ecommerce-store',
    title: 'E-commerce Store',
    shortDescription: 'Complete online store with payment processing',
    description: 'Full-featured e-commerce platform with inventory management, payment processing, and admin dashboard.',
    icon: ShoppingCart,
    category: productCategories[1],
    features: [
      'Product catalog management',
      'Shopping cart & checkout',
      'Payment gateway integration',
      'Order management system',
      'Inventory tracking',
      'Customer accounts',
      'Admin dashboard',
      'SEO optimization'
    ],
    technologies: ['Shopify', 'WooCommerce', 'Stripe', 'PayPal', 'React', 'Node.js'],
    pricing: {
      type: 'starting',
      amount: 3500,
      currency: 'USD',
      note: 'Includes setup and basic customization'
    },
    popular: true,
    caseStudy: {
      title: 'Luxury Jewelry Store Success',
      description: 'Increased online sales by 300% with custom e-commerce solution',
      challenge: 'Client needed a sophisticated online presence for high-end jewelry',
      solution: 'Custom Shopify theme with advanced product visualization',
      results: [
        '300% increase in online sales',
        '45% higher average order value',
        '60% improvement in conversion rate'
      ]
    }
  },
  {
    id: 'mobile-app',
    title: 'Mobile Application',
    shortDescription: 'Native iOS and Android applications',
    description: 'Cross-platform mobile applications built with React Native or native technologies for optimal performance.',
    icon: Smartphone,
    category: productCategories[2],
    features: [
      'Cross-platform development',
      'Native performance',
      'Push notifications',
      'Offline functionality',
      'App store deployment',
      'User authentication',
      'API integration',
      'Analytics integration'
    ],
    technologies: ['React Native', 'Flutter', 'Swift', 'Kotlin', 'Firebase'],
    pricing: {
      type: 'starting',
      amount: 8000,
      currency: 'USD',
      note: 'Includes both iOS and Android versions'
    },
    featured: true
  },
  {
    id: 'cloud-infrastructure',
    title: 'Cloud Infrastructure Setup',
    shortDescription: 'Scalable cloud architecture and deployment',
    description: 'Complete cloud infrastructure setup with auto-scaling, monitoring, and security best practices.',
    icon: Cloud,
    category: productCategories[3],
    features: [
      'AWS/Azure/GCP setup',
      'Auto-scaling configuration',
      'Load balancing',
      'Database optimization',
      'Security hardening',
      'Monitoring & alerts',
      'Backup strategies',
      'Cost optimization'
    ],
    technologies: ['AWS', 'Docker', 'Kubernetes', 'Terraform', 'CloudFormation'],
    pricing: {
      type: 'starting',
      amount: 2500,
      currency: 'USD',
      note: 'Ongoing support available'
    }
  },
  {
    id: 'technical-consulting',
    title: 'Technical Consulting',
    shortDescription: 'Expert guidance for your technology decisions',
    description: 'Strategic technical consulting to help you make informed decisions about your technology stack and architecture.',
    icon: Users,
    category: productCategories[4],
    features: [
      'Technology stack assessment',
      'Architecture review',
      'Performance optimization',
      'Security audit',
      'Code review',
      'Team training',
      'Best practices guidance',
      'Strategic planning'
    ],
    technologies: ['Various', 'Architecture', 'Security', 'Performance'],
    pricing: {
      type: 'custom',
      note: 'Hourly or project-based rates available'
    }
  },
  {
    id: 'cms-solution',
    title: 'Content Management System',
    shortDescription: 'Custom CMS for easy content management',
    description: 'Headless CMS solutions that give you full control over your content with modern editing interfaces.',
    icon: Database,
    category: productCategories[0],
    features: [
      'Headless CMS architecture',
      'Custom content types',
      'Multi-language support',
      'Media management',
      'User roles & permissions',
      'API-first approach',
      'SEO optimization',
      'Version control'
    ],
    technologies: ['Sanity', 'Strapi', 'Contentful', 'GraphQL', 'React'],
    pricing: {
      type: 'starting',
      amount: 4000,
      currency: 'USD',
      note: 'Includes setup and training'
    },
    popular: true
  }
];

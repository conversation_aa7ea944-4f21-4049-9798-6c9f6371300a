import { Suspense, lazy } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { ThemeProvider } from "@/components/ThemeProvider";
import { LanguageProvider } from "@/i18n/LanguageProvider";
import { ScrollToTop } from "@/components/ScrollToTop";
import { PageTransition } from "@/components/PageTransition";
import { SanityPortfolioProvider } from "@/components/SanityPortfolioProvider";
import { SanityBlogProvider } from "@/components/SanityBlogProvider";
import { WhatsAppButton } from "@/components/WhatsAppButton";
import { portfolioItems } from "@/data/portfolio";
import { blogPosts } from "@/data/blog-posts";
import { LoadingState } from "@/components/ui/loading-state";
import { ErrorBoundary } from "@/components/ui/error-boundary";

// Group related routes into logical chunks
const Index = lazy(() => import(/* webpackChunkName: "home" */ "./pages/Index"));
const Services = lazy(() => import(/* webpackChunkName: "services" */ "./pages/Services"));
const About = lazy(() => import(/* webpackChunkName: "company" */ "./pages/About"));
const Contact = lazy(() => import(/* webpackChunkName: "company" */ "./pages/Contact"));
const Portfolio = lazy(() => import(/* webpackChunkName: "portfolio" */ "./pages/Portfolio"));
const Privacy = lazy(() => import(/* webpackChunkName: "legal" */ "./pages/Privacy"));
const Terms = lazy(() => import(/* webpackChunkName: "legal" */ "./pages/Terms"));
const NotFound = lazy(() => import(/* webpackChunkName: "misc" */ "./pages/NotFound"));

// Prefetch critical chunks
const prefetchRoutes = () => {
  // Prefetch the services chunk as it's commonly accessed
  const link = document.createElement('link');
  link.rel = 'modulepreload';
  link.href = '/src/pages/Services.tsx';
  document.head.appendChild(link);
};

// Execute prefetch after initial render
if (typeof window !== 'undefined') {
  window.addEventListener('load', prefetchRoutes);
}

// Configure QueryClient with optimized settings
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 1000 * 60 * 5, // Data stays fresh for 5 minutes
      gcTime: 1000 * 60 * 30, // Garbage collection after 30 minutes
      retry: 2,
      refetchOnWindowFocus: false
    },
    mutations: {
      retry: 1
    }
  }
});

// Reusable route wrapper with Suspense and ErrorBoundary
interface RouteWrapperProps {
  children: React.ReactNode;
  pageName: string;
}

const RouteWrapper = ({ children, pageName }: RouteWrapperProps) => (
  <ErrorBoundary>
    <Suspense fallback={
      <LoadingState
        message={`Loading ${pageName}...`}
        size="md"
        className="m-4 rounded-lg shadow-sm"
      />
    }>
      {children}
    </Suspense>
  </ErrorBoundary>
);

const App = () => {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <ThemeProvider defaultTheme="light" storageKey="codesafir-theme">
          <LanguageProvider>
            <TooltipProvider>
              <Toaster />
              <Sonner />
              {/* Provide Sanity data with local data as fallback */}
              <SanityPortfolioProvider fallbackData={portfolioItems}>
                <SanityBlogProvider fallbackData={blogPosts}>
                  <BrowserRouter>
                    <ScrollToTop />
                    <PageTransition>
                      <Routes>
                        <Route path="/" element={
                          <RouteWrapper pageName="Home">
                            <Index />
                          </RouteWrapper>
                        } />
                        <Route path="/services" element={
                          <RouteWrapper pageName="Services">
                            <Services />
                          </RouteWrapper>
                        } />
                        <Route path="/about" element={
                          <RouteWrapper pageName="About">
                            <About />
                          </RouteWrapper>
                        } />
                        <Route path="/contact" element={
                          <RouteWrapper pageName="Contact">
                            <Contact />
                          </RouteWrapper>
                        } />
                        <Route path="/portfolio" element={
                          <RouteWrapper pageName="Portfolio">
                            <Portfolio />
                          </RouteWrapper>
                        } />
                        <Route path="/portfolio/:slug" element={
                          <RouteWrapper pageName="Portfolio Details">
                            <Portfolio />
                          </RouteWrapper>
                        } />
                        <Route path="/services/:slug" element={
                          <RouteWrapper pageName="Service Details">
                            <Services />
                          </RouteWrapper>
                        } />
                        <Route path="/privacy" element={
                          <RouteWrapper pageName="Privacy Policy">
                            <Privacy />
                          </RouteWrapper>
                        } />
                        <Route path="/terms" element={
                          <RouteWrapper pageName="Terms of Service">
                            <Terms />
                          </RouteWrapper>
                        } />
                        {/* ADD ALL CUSTOM ROUTES ABOVE THE CATCH-ALL "*" ROUTE */}
                        <Route path="*" element={
                          <RouteWrapper pageName="Not Found">
                            <NotFound />
                          </RouteWrapper>
                        } />
                      </Routes>
                    </PageTransition>
                    {/* Global WhatsApp Button */}
                    <WhatsAppButton phoneNumber="+201064149151" message="" />
                  </BrowserRouter>
                </SanityBlogProvider>
              </SanityPortfolioProvider>
            </TooltipProvider>
          </LanguageProvider>
        </ThemeProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
};

export default App;
